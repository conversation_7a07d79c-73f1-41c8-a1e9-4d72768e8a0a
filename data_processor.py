#!/usr/bin/env python3
"""
Data Processor for TopCV Crawled Data
Clean and process data for database import
"""

import json
import csv
import re
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
import pandas as pd

class DataProcessor:
    def __init__(self):
        self.companies = []
        self.jobs = []
        
    def load_data(self, json_file: str = "topcv_data.json"):
        """Load data from JSON file"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.companies = data.get('companies', [])
                self.jobs = data.get('jobs', [])
            print(f"Loaded {len(self.companies)} companies and {len(self.jobs)} jobs")
        except FileNotFoundError:
            print(f"File {json_file} not found. Please run the crawler first.")
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text data"""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters that might cause issues
        text = re.sub(r'[^\w\s\-.,():/]', '', text)
        
        return text
    
    def normalize_salary(self, salary_text: str) -> Optional[float]:
        """Extract and normalize salary from text"""
        if not salary_text or salary_text.lower() in ['thoả thuận', 'thỏa thuận', 'negotiate']:
            return None
        
        # Extract numbers from salary text
        numbers = re.findall(r'(\d+(?:\.\d+)?)', salary_text.lower())
        
        if not numbers:
            return None
        
        # Handle different formats
        if 'triệu' in salary_text.lower():
            if len(numbers) >= 2:
                # Range: "15 - 20 triệu"
                min_sal = float(numbers[0]) * 1000000
                max_sal = float(numbers[1]) * 1000000
                return (min_sal + max_sal) / 2
            else:
                # Single value: "15 triệu"
                return float(numbers[0]) * 1000000
        elif 'usd' in salary_text.lower():
            if len(numbers) >= 2:
                min_sal = float(numbers[0]) * 25000  # Approximate VND conversion
                max_sal = float(numbers[1]) * 25000
                return (min_sal + max_sal) / 2
            else:
                return float(numbers[0]) * 25000
        
        return None
    
    def extract_skills(self, text: str) -> List[str]:
        """Extract technical skills from job description"""
        if not text:
            return []
        
        # Common technical skills
        skill_patterns = [
            r'\b(Java|Python|JavaScript|TypeScript|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin)\b',
            r'\b(React|Angular|Vue|Node\.js|Express|Django|Flask|Spring|Laravel)\b',
            r'\b(MySQL|PostgreSQL|MongoDB|Redis|Oracle|SQL Server)\b',
            r'\b(Docker|Kubernetes|AWS|Azure|GCP|Jenkins|Git|GitLab)\b',
            r'\b(HTML|CSS|SASS|LESS|Bootstrap|Tailwind)\b',
            r'\b(REST|GraphQL|API|Microservices|DevOps|CI/CD)\b'
        ]
        
        skills = set()
        text_upper = text.upper()
        
        for pattern in skill_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            skills.update(matches)
        
        return list(skills)
    
    def standardize_location(self, location: str) -> str:
        """Standardize location names"""
        if not location:
            return ""
        
        location_mapping = {
            'hà nội': 'Hà Nội',
            'ho chi minh': 'Hồ Chí Minh',
            'hcm': 'Hồ Chí Minh',
            'tp.hcm': 'Hồ Chí Minh',
            'da nang': 'Đà Nẵng',
            'hai phong': 'Hải Phòng',
            'can tho': 'Cần Thơ',
            'binh duong': 'Bình Dương',
            'dong nai': 'Đồng Nai'
        }
        
        location_lower = location.lower().strip()
        return location_mapping.get(location_lower, location.title())
    
    def clean_companies(self):
        """Clean and process company data"""
        cleaned_companies = []
        
        for company in self.companies:
            cleaned_company = {
                'id': company.get('id', str(uuid.uuid4())),
                'companyName': self.clean_text(company.get('companyName', '')),
                'industry': self.clean_text(company.get('industry', '')),
                'website': company.get('website', '').strip(),
                'description': self.clean_text(company.get('description', '')),
                'createdAt': company.get('createdAt', datetime.now().isoformat())
            }
            
            # Validate required fields
            if cleaned_company['companyName']:
                cleaned_companies.append(cleaned_company)
        
        self.companies = cleaned_companies
        print(f"Cleaned {len(self.companies)} companies")
    
    def clean_jobs(self):
        """Clean and process job data"""
        cleaned_jobs = []
        
        for job in self.jobs:
            # Extract skills from description
            description = job.get('description', '')
            requirements = job.get('requirements', '')
            combined_text = f"{description} {requirements}"
            skills = self.extract_skills(combined_text)
            
            cleaned_job = {
                'id': job.get('id', str(uuid.uuid4())),
                'companyId': job.get('companyId', ''),
                'title': self.clean_text(job.get('title', '')),
                'description': self.clean_text(job.get('description', '')),
                'salary': self.normalize_salary(str(job.get('salary', ''))),
                'location': self.standardize_location(job.get('location', '')),
                'jobType': job.get('jobType', 'Toàn thời gian'),
                'status': job.get('status', 'Active'),
                'postedAt': job.get('postedAt', datetime.now().isoformat()),
                'expiresAt': job.get('expiresAt'),
                'viewCount': int(job.get('viewCount', 0)),
                'requirements': self.clean_text(job.get('requirements', '')),
                'benefits': self.clean_text(job.get('benefits', '')),
                'skills': skills,
                'experience': self.clean_text(job.get('experience', ''))
            }
            
            # Validate required fields
            if cleaned_job['title'] and cleaned_job['companyId']:
                cleaned_jobs.append(cleaned_job)
        
        self.jobs = cleaned_jobs
        print(f"Cleaned {len(self.jobs)} jobs")
    
    def generate_sql_inserts(self, output_file: str = "database_inserts.sql"):
        """Generate SQL INSERT statements for database import"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- TopCV Data Import SQL\n")
            f.write("-- Generated on: " + datetime.now().isoformat() + "\n\n")
            
            # Insert companies
            f.write("-- Insert Companies\n")
            for company in self.companies:
                sql = f"""INSERT INTO "tblCompanies" ("id", "companyName", "industry", "website", "description", "createdAt") 
VALUES ('{company['id']}', '{company['companyName'].replace("'", "''")}', '{company['industry'].replace("'", "''")}', 
'{company['website']}', '{company['description'].replace("'", "''")}', '{company['createdAt']}');\n"""
                f.write(sql)
            
            f.write("\n-- Insert Jobs\n")
            for job in self.jobs:
                salary_val = job['salary'] if job['salary'] is not None else 'NULL'
                expires_val = f"'{job['expiresAt']}'" if job['expiresAt'] else 'NULL'
                
                sql = f"""INSERT INTO "tblJobPostings" ("id", "companyId", "title", "description", "salary", "location", "jobType", "status", "postedAt", "expiresAt", "viewCount") 
VALUES ('{job['id']}', '{job['companyId']}', '{job['title'].replace("'", "''")}', '{job['description'].replace("'", "''")}', 
{salary_val}, '{job['location']}', '{job['jobType']}', '{job['status']}', '{job['postedAt']}', {expires_val}, {job['viewCount']});\n"""
                f.write(sql)
        
        print(f"SQL inserts generated in {output_file}")
    
    def export_to_excel(self, output_file: str = "topcv_data_cleaned.xlsx"):
        """Export cleaned data to Excel file"""
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # Companies sheet
            companies_df = pd.DataFrame(self.companies)
            companies_df.to_excel(writer, sheet_name='Companies', index=False)
            
            # Jobs sheet
            jobs_df = pd.DataFrame(self.jobs)
            # Convert skills list to string for Excel
            jobs_df['skills'] = jobs_df['skills'].apply(lambda x: ', '.join(x) if isinstance(x, list) else '')
            jobs_df.to_excel(writer, sheet_name='Jobs', index=False)
        
        print(f"Data exported to {output_file}")
    
    def generate_summary_report(self):
        """Generate a summary report of the cleaned data"""
        print("\n=== DATA SUMMARY REPORT ===")
        print(f"Total Companies: {len(self.companies)}")
        print(f"Total Jobs: {len(self.jobs)}")
        
        # Industry distribution
        industries = [comp['industry'] for comp in self.companies if comp['industry']]
        industry_counts = {}
        for industry in industries:
            industry_counts[industry] = industry_counts.get(industry, 0) + 1
        
        print("\nTop Industries:")
        for industry, count in sorted(industry_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {industry}: {count}")
        
        # Location distribution
        locations = [job['location'] for job in self.jobs if job['location']]
        location_counts = {}
        for location in locations:
            location_counts[location] = location_counts.get(location, 0) + 1
        
        print("\nTop Locations:")
        for location, count in sorted(location_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {location}: {count}")
        
        # Salary statistics
        salaries = [job['salary'] for job in self.jobs if job['salary'] is not None]
        if salaries:
            print(f"\nSalary Statistics:")
            print(f"  Average: {sum(salaries)/len(salaries):,.0f} VND")
            print(f"  Min: {min(salaries):,.0f} VND")
            print(f"  Max: {max(salaries):,.0f} VND")

def main():
    """Main function to process the data"""
    processor = DataProcessor()
    
    print("Loading crawled data...")
    processor.load_data()
    
    print("Cleaning companies data...")
    processor.clean_companies()
    
    print("Cleaning jobs data...")
    processor.clean_jobs()
    
    print("Generating SQL inserts...")
    processor.generate_sql_inserts()
    
    print("Exporting to Excel...")
    processor.export_to_excel()
    
    print("Generating summary report...")
    processor.generate_summary_report()
    
    print("\nData processing completed successfully!")
    print("\nGenerated files:")
    print("- database_inserts.sql (SQL INSERT statements)")
    print("- topcv_data_cleaned.xlsx (Excel file with cleaned data)")

if __name__ == "__main__":
    main()
