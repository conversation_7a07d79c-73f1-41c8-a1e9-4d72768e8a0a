{"companies": [{"id": "cd41d44d-588e-431b-a91c-c3d3d8dfc89d", "companyName": "NGÂN HÀNG THƯƠNG MẠI CỔ PHẦN QUÂN ĐỘI", "industry": "Công nghệ thông tin", "website": "https://www.ngânhàngth.com", "description": "Công ty hàng đầu trong lĩnh vực công nghệ thông tin. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022147"}, {"id": "316ae71b-f552-4c60-8880-685fdef5b3d7", "companyName": "CÔNG TY CỔ PHẦN SMARTOSC", "industry": "<PERSON><PERSON> h<PERSON>ng - <PERSON><PERSON><PERSON> ch<PERSON>h", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực ngân hàng - tài ch<PERSON>h. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022172"}, {"id": "b5858630-ee9e-45c4-8b36-a2c5da554717", "companyName": "BÁO ĐIỆN TỬ VNEXPRESS.NET", "industry": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử", "website": "https://www.báođiệntửv.com", "description": "Công ty hàng đầu trong lĩnh vực thương mại điện tử. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022181"}, {"id": "f076fffc-e914-40cb-b535-c749ccd6482f", "companyName": "<PERSON><PERSON><PERSON> ty <PERSON> phần <PERSON>", "industry": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> t<PERSON>o", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực gi<PERSON>o dục - đào tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022189"}, {"id": "83ac64cb-3eb4-49ac-8176-d9641907bed9", "companyName": "CÔNG TY CỔ PHẦN NGHIÊN CỨU VÀ PHÁT TRIỂN DICOM", "industry": "<PERSON> tế - <PERSON><PERSON><PERSON><PERSON> phẩm", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực y tế - <PERSON><PERSON><PERSON><PERSON> phẩm. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022197"}, {"id": "63986696-ed5b-4660-a849-681935fda1a7", "companyName": "<PERSON><PERSON>ng ty <PERSON> ph<PERSON>n <PERSON>", "industry": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực bất động sản. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022207"}, {"id": "f7796c5a-4078-4607-b794-8fe7f4899071", "companyName": "CÔNG TY TNHH SWGAME", "industry": "<PERSON><PERSON><PERSON> xuất - <PERSON><PERSON> tạo", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực sản xuất - ch<PERSON> tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022214"}, {"id": "37ea4ec7-3539-41c2-bf3c-0ca373249b0e", "companyName": "CÔNG TY CỔ PHẦN IKAME GLOBAL", "industry": "<PERSON><PERSON><PERSON> v<PERSON> kh<PERSON>ch hàng", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực dịch vụ khách hàng. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022222"}, {"id": "36bb8c0a-3af2-4ac0-b9ec-9a8a2fbeede1", "companyName": "CÔNG TY TNHH ATEC SYSTEM VIỆT NAM", "industry": "Marketing - <PERSON><PERSON><PERSON><PERSON><PERSON> thông", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực marketing - truyền thông. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022229"}, {"id": "993ed64a-641f-4132-b5ad-40e69c45a311", "companyName": "CÔNG TY CỔ PHẦN GIẢI PHÁP ATOM", "industry": "Logistics - <PERSON><PERSON><PERSON>", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực logistics - vận tải. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022236"}, {"id": "b943a2a6-e3b5-4f3f-9fff-1b34cb25f76d", "companyName": "CÔNG TY CỔ PHẦN ADVANCE TECH VIỆT NAM", "industry": "Công nghệ thông tin", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực công nghệ thông tin. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022244"}, {"id": "f03090b8-7977-42b3-9072-7f6b2803c5cb", "companyName": "CÔNG TY TNHH THINKWARE VIỆT NAM", "industry": "<PERSON><PERSON> h<PERSON>ng - <PERSON><PERSON><PERSON> ch<PERSON>h", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực ngân hàng - tài ch<PERSON>h. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022251"}, {"id": "60dcf246-6467-4b53-88b7-0b1c6e1ebaea", "companyName": "TV TPI CO., LTD", "industry": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử", "website": "https://www.tvtpico.,l.com", "description": "Công ty hàng đầu trong lĩnh vực thương mại điện tử. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022258"}, {"id": "5c3dbb0a-3b73-43b2-9e3c-50dddaa506c8", "companyName": "Công ty TNHH Truyền thông công nghệ Ditech", "industry": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> t<PERSON>o", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực gi<PERSON>o dục - đào tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022265"}, {"id": "d92e0b47-f2ab-47f3-acab-ba90e7d3448f", "companyName": "CÔNG TY CỔ PHẦN GIẢI PHÁP CÔNG NGHỆ CARO", "industry": "<PERSON> tế - <PERSON><PERSON><PERSON><PERSON> phẩm", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực y tế - <PERSON><PERSON><PERSON><PERSON> phẩm. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022272"}, {"id": "f3100a86-8fb5-4880-93b8-22028c42a832", "companyName": "Công Ty TNHH Thương Mại - <PERSON><PERSON><PERSON>", "industry": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực bất động sản. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022279"}, {"id": "09389b19-408f-4c77-90cd-9299d13577a1", "companyName": "CÔNG TY CỔ PHẦN CÔNG NGHỆ CAO ALTISSS", "industry": "<PERSON><PERSON><PERSON> xuất - <PERSON><PERSON> tạo", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực sản xuất - ch<PERSON> tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022287"}, {"id": "960fb80a-b02a-47f2-95e6-336f3e262e4a", "companyName": "CÔNG TY TNHH ĐỊA ỐC MOSO", "industry": "<PERSON><PERSON><PERSON> v<PERSON> kh<PERSON>ch hàng", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực dịch vụ khách hàng. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022294"}, {"id": "7a5a370b-b707-499c-9613-c7b422a07b0c", "companyName": "CÔNG TY CỔ PHẦN TINASOFT VIỆT NAM", "industry": "Marketing - <PERSON><PERSON><PERSON><PERSON><PERSON> thông", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực marketing - truyền thông. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022303"}, {"id": "7c91ff89-15fa-4f2d-b325-672e4ffa83a9", "companyName": "MARUSYS VINA CO.,LTD", "industry": "Logistics - <PERSON><PERSON><PERSON>", "website": "https://www.marusysvin.com", "description": "Công ty hàng đầu trong lĩnh vực logistics - vận tải. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022309"}, {"id": "b88f03d1-6463-4987-8972-0db2c64d40af", "companyName": "FPT Software", "industry": "Công nghệ thông tin", "website": "https://www.fptsoftwar.com", "description": "Công ty hàng đầu trong lĩnh vực công nghệ thông tin. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022316"}, {"id": "89924d54-c2b0-45a7-858f-d8c748b9bbab", "companyName": "Công ty TNHH 1C Việt Nam", "industry": "<PERSON><PERSON> h<PERSON>ng - <PERSON><PERSON><PERSON> ch<PERSON>h", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực ngân hàng - tài ch<PERSON>h. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022323"}, {"id": "65e93fbc-4d7d-4dc7-aa33-4cbbcb74f55a", "companyName": "<PERSON>ông ty cổ phần Công nghệ thông tin <PERSON>", "industry": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực thương mại điện tử. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022330"}, {"id": "ec37c511-ca75-4f24-8edc-71c86d41e664", "companyName": "<PERSON><PERSON><PERSON> ty cổ phần PayPay", "industry": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> t<PERSON>o", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực gi<PERSON>o dục - đào tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022337"}, {"id": "3cad333b-560a-477c-b621-1f9024b0d29f", "companyName": "<PERSON><PERSON><PERSON> ty cổ phần <PERSON>us", "industry": "<PERSON> tế - <PERSON><PERSON><PERSON><PERSON> phẩm", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực y tế - <PERSON><PERSON><PERSON><PERSON> phẩm. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022347"}, {"id": "4d6d0617-ecfa-4b54-9e94-dad160f59f11", "companyName": "NGÂN HÀNG THƯƠNG MẠI CỔ PHẦN QUÂN ĐỘI", "industry": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "website": "https://www.ngânhàngth.com", "description": "Công ty hàng đầu trong lĩnh vực bất động sản. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022358"}, {"id": "58faf246-c7ed-485b-8e0c-353fa9671d12", "companyName": "CÔNG TY CỔ PHẦN SMARTOSC", "industry": "<PERSON><PERSON><PERSON> xuất - <PERSON><PERSON> tạo", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực sản xuất - ch<PERSON> tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022368"}, {"id": "8845db93-f7df-49fe-94f4-021955487d74", "companyName": "BÁO ĐIỆN TỬ VNEXPRESS.NET", "industry": "<PERSON><PERSON><PERSON> v<PERSON> kh<PERSON>ch hàng", "website": "https://www.báođiệntửv.com", "description": "Công ty hàng đầu trong lĩnh vực dịch vụ khách hàng. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022378"}, {"id": "6710cf7f-a793-4822-8b55-0d79cd3045e1", "companyName": "<PERSON><PERSON><PERSON> ty <PERSON> phần <PERSON>", "industry": "Marketing - <PERSON><PERSON><PERSON><PERSON><PERSON> thông", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực marketing - truyền thông. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022388"}, {"id": "7bb1bcc2-6dc1-4ce1-add9-b7e767481da7", "companyName": "CÔNG TY CỔ PHẦN NGHIÊN CỨU VÀ PHÁT TRIỂN DICOM", "industry": "Logistics - <PERSON><PERSON><PERSON>", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực logistics - vận tải. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022399"}, {"id": "c0ac624a-f19b-494b-9957-e868db761f6f", "companyName": "<PERSON><PERSON>ng ty <PERSON> ph<PERSON>n <PERSON>", "industry": "Công nghệ thông tin", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực công nghệ thông tin. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022409"}, {"id": "4f15f43d-1c94-4b89-be83-d23ded9acb1e", "companyName": "CÔNG TY TNHH SWGAME", "industry": "<PERSON><PERSON> h<PERSON>ng - <PERSON><PERSON><PERSON> ch<PERSON>h", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực ngân hàng - tài ch<PERSON>h. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022422"}, {"id": "3d88ac25-2ffa-4a22-b8c1-75dd0a89186d", "companyName": "CÔNG TY CỔ PHẦN IKAME GLOBAL", "industry": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực thương mại điện tử. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022432"}, {"id": "7b99264c-c034-40a4-b4d4-65204f4235a7", "companyName": "CÔNG TY TNHH ATEC SYSTEM VIỆT NAM", "industry": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> t<PERSON>o", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực gi<PERSON>o dục - đào tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022443"}, {"id": "fa635ac1-758c-4df2-a506-e747f6c8d8ac", "companyName": "CÔNG TY CỔ PHẦN GIẢI PHÁP ATOM", "industry": "<PERSON> tế - <PERSON><PERSON><PERSON><PERSON> phẩm", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực y tế - <PERSON><PERSON><PERSON><PERSON> phẩm. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022453"}, {"id": "5446b769-e122-4ae4-ba17-0b5904cdd61e", "companyName": "CÔNG TY CỔ PHẦN ADVANCE TECH VIỆT NAM", "industry": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực bất động sản. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022463"}, {"id": "9785d6dc-05ca-48b4-a55a-84b6d28e43f0", "companyName": "CÔNG TY TNHH THINKWARE VIỆT NAM", "industry": "<PERSON><PERSON><PERSON> xuất - <PERSON><PERSON> tạo", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực sản xuất - ch<PERSON> tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022473"}, {"id": "c31045a1-19de-479b-a4da-a32476617640", "companyName": "TV TPI CO., LTD", "industry": "<PERSON><PERSON><PERSON> v<PERSON> kh<PERSON>ch hàng", "website": "https://www.tvtpico.,l.com", "description": "Công ty hàng đầu trong lĩnh vực dịch vụ khách hàng. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022483"}, {"id": "513042ef-0d07-4f7c-a655-1cf33d7ee126", "companyName": "Công ty TNHH Truyền thông công nghệ Ditech", "industry": "Marketing - <PERSON><PERSON><PERSON><PERSON><PERSON> thông", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực marketing - truyền thông. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022494"}, {"id": "da3d42a2-81c8-4a45-a362-8861d0ac1db3", "companyName": "CÔNG TY CỔ PHẦN GIẢI PHÁP CÔNG NGHỆ CARO", "industry": "Logistics - <PERSON><PERSON><PERSON>", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực logistics - vận tải. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022504"}, {"id": "7f84aa21-026c-4d77-b72b-08fa60c10a08", "companyName": "Công Ty TNHH Thương Mại - <PERSON><PERSON><PERSON>", "industry": "Công nghệ thông tin", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực công nghệ thông tin. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022514"}, {"id": "04329498-f953-42c7-8828-8cb225fc3456", "companyName": "CÔNG TY CỔ PHẦN CÔNG NGHỆ CAO ALTISSS", "industry": "<PERSON><PERSON> h<PERSON>ng - <PERSON><PERSON><PERSON> ch<PERSON>h", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực ngân hàng - tài ch<PERSON>h. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022525"}, {"id": "2b59e33a-2ef0-45ed-80db-dda656d2a045", "companyName": "CÔNG TY TNHH ĐỊA ỐC MOSO", "industry": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực thương mại điện tử. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022535"}, {"id": "6d9250ca-9898-4b12-98a1-edb90d23e77a", "companyName": "CÔNG TY CỔ PHẦN TINASOFT VIỆT NAM", "industry": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> t<PERSON>o", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực gi<PERSON>o dục - đào tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022545"}, {"id": "61d26503-1385-40db-bb2f-c2f3b3b6cfc9", "companyName": "MARUSYS VINA CO.,LTD", "industry": "<PERSON> tế - <PERSON><PERSON><PERSON><PERSON> phẩm", "website": "https://www.marusysvin.com", "description": "Công ty hàng đầu trong lĩnh vực y tế - <PERSON><PERSON><PERSON><PERSON> phẩm. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022560"}, {"id": "f7bb60a9-aeb1-4398-a3cc-093a0791be1e", "companyName": "FPT Software", "industry": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "website": "https://www.fptsoftwar.com", "description": "Công ty hàng đầu trong lĩnh vực bất động sản. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022567"}, {"id": "53619a4c-ab32-4215-9729-074bf961a995", "companyName": "Công ty TNHH 1C Việt Nam", "industry": "<PERSON><PERSON><PERSON> xuất - <PERSON><PERSON> tạo", "website": "https://www.côngtytnhh.com", "description": "Công ty hàng đầu trong lĩnh vực sản xuất - ch<PERSON> tạo. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022574"}, {"id": "fb08a870-4cef-40d6-a846-103b4227db2b", "companyName": "<PERSON>ông ty cổ phần Công nghệ thông tin <PERSON>", "industry": "<PERSON><PERSON><PERSON> v<PERSON> kh<PERSON>ch hàng", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực dịch vụ khách hàng. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022582"}, {"id": "0879b0c5-3b5e-4df6-a1b9-437243ae7695", "companyName": "<PERSON><PERSON><PERSON> ty cổ phần PayPay", "industry": "Marketing - <PERSON><PERSON><PERSON><PERSON><PERSON> thông", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực marketing - truyền thông. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022589"}, {"id": "4e435fa3-d7dd-4cf3-b2c0-7bd460e78841", "companyName": "<PERSON><PERSON><PERSON> ty cổ phần <PERSON>us", "industry": "Logistics - <PERSON><PERSON><PERSON>", "website": "https://www.côngtycổph.com", "description": "Công ty hàng đầu trong lĩnh vực logistics - vận tải. Chúng tôi cam kết mang đến môi trường làm việc chuyên nghiệp và cơ hội phát triển tốt nhất cho nhân viên.", "createdAt": "2025-07-16T01:19:58.022595"}], "jobs": [{"id": "aa8a0e81-7f3e-4744-a505-f6a59c9f3d6a", "companyId": "cd41d44d-588e-431b-a91c-c3d3d8dfc89d", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022603", "expiresAt": null, "viewCount": 0, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "94223ed8-99db-40cd-8cc4-04a140c68e64", "companyId": "316ae71b-f552-4c60-8880-685fdef5b3d7", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022611", "expiresAt": null, "viewCount": 17, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "ef0e91f6-af64-4fa9-84b7-f460c5006630", "companyId": "b5858630-ee9e-45c4-8b36-a2c5da554717", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022618", "expiresAt": null, "viewCount": 34, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "23584305-1898-420a-ba1c-1fe29d145606", "companyId": "f076fffc-e914-40cb-b535-c749ccd6482f", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022625", "expiresAt": null, "viewCount": 51, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "f353f1ae-364c-4c1c-81fb-a2fb678bbf2f", "companyId": "83ac64cb-3eb4-49ac-8176-d9641907bed9", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022632", "expiresAt": null, "viewCount": 68, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "f5c73bab-36be-4f49-82cc-47c306ed5b7e", "companyId": "63986696-ed5b-4660-a849-681935fda1a7", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022639", "expiresAt": null, "viewCount": 85, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "3914ec09-0f77-496d-b132-cbe1258824ec", "companyId": "f7796c5a-4078-4607-b794-8fe7f4899071", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022646", "expiresAt": null, "viewCount": 102, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "5bb75918-8e77-4fd0-a879-efaff359550b", "companyId": "37ea4ec7-3539-41c2-bf3c-0ca373249b0e", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022653", "expiresAt": null, "viewCount": 119, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "8d99b7b2-dae7-4ddf-9d66-6a364883dade", "companyId": "36bb8c0a-3af2-4ac0-b9ec-9a8a2fbeede1", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022659", "expiresAt": null, "viewCount": 136, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "7cbf6aaf-c229-4a61-8cb6-00a4a5bbcf9b", "companyId": "993ed64a-641f-4132-b5ad-40e69c45a311", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022666", "expiresAt": null, "viewCount": 153, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "7036efab-ae88-4e11-94c8-33f2dbedae8b", "companyId": "b943a2a6-e3b5-4f3f-9fff-1b34cb25f76d", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022673", "expiresAt": null, "viewCount": 170, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "e192f308-9999-4b17-a386-024c3828744d", "companyId": "f03090b8-7977-42b3-9072-7f6b2803c5cb", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022679", "expiresAt": null, "viewCount": 187, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "4b0af036-7083-4892-9b2b-45ccc39c1759", "companyId": "60dcf246-6467-4b53-88b7-0b1c6e1ebaea", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022686", "expiresAt": null, "viewCount": 204, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "95b5de2c-f533-4cea-b70e-7b34bcf143b9", "companyId": "5c3dbb0a-3b73-43b2-9e3c-50dddaa506c8", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022693", "expiresAt": null, "viewCount": 221, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "60fa3990-2b3c-4169-bd2c-c6a03729f01b", "companyId": "d92e0b47-f2ab-47f3-acab-ba90e7d3448f", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022699", "expiresAt": null, "viewCount": 238, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "8ce4401c-915e-4485-8104-5a1989f42bef", "companyId": "f3100a86-8fb5-4880-93b8-22028c42a832", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022706", "expiresAt": null, "viewCount": 255, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "de83fc64-285d-4ba1-b934-15cbd4f67b7c", "companyId": "09389b19-408f-4c77-90cd-9299d13577a1", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022713", "expiresAt": null, "viewCount": 272, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "d765c319-bce8-4a13-a37c-8508eb6adbb3", "companyId": "960fb80a-b02a-47f2-95e6-336f3e262e4a", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022719", "expiresAt": null, "viewCount": 289, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "f91f498b-25c8-4d8e-963a-a6fc85cf3a22", "companyId": "7a5a370b-b707-499c-9613-c7b422a07b0c", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022726", "expiresAt": null, "viewCount": 306, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "fe5ef930-8ba1-4d45-8d8d-fb21cb8d7955", "companyId": "7c91ff89-15fa-4f2d-b325-672e4ffa83a9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022733", "expiresAt": null, "viewCount": 323, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "46121a75-ea58-4a65-98b8-508c803dae8b", "companyId": "b88f03d1-6463-4987-8972-0db2c64d40af", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022739", "expiresAt": null, "viewCount": 340, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "b429abe0-c54f-4b7c-baf5-d00a5bb7487b", "companyId": "89924d54-c2b0-45a7-858f-d8c748b9bbab", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022746", "expiresAt": null, "viewCount": 357, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "39071b47-a030-4422-b871-e504c3343ab0", "companyId": "65e93fbc-4d7d-4dc7-aa33-4cbbcb74f55a", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022754", "expiresAt": null, "viewCount": 374, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "aa16393e-dd61-45f0-a112-4944c58828c8", "companyId": "ec37c511-ca75-4f24-8edc-71c86d41e664", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022760", "expiresAt": null, "viewCount": 391, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "ca40a5a3-e684-401d-b23e-f5d673a94fc7", "companyId": "3cad333b-560a-477c-b621-1f9024b0d29f", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022769", "expiresAt": null, "viewCount": 408, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "c003506c-ce20-4d60-968d-d1ff872f8a5b", "companyId": "4d6d0617-ecfa-4b54-9e94-dad160f59f11", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022775", "expiresAt": null, "viewCount": 425, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "947d42fa-560b-466d-bdc9-937bc5a4f670", "companyId": "58faf246-c7ed-485b-8e0c-353fa9671d12", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022782", "expiresAt": null, "viewCount": 442, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "be779fc6-0a75-4a9d-a231-ce0862156da6", "companyId": "8845db93-f7df-49fe-94f4-021955487d74", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022789", "expiresAt": null, "viewCount": 459, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "60b833f3-0abd-4980-a2a6-0e9747df5e42", "companyId": "6710cf7f-a793-4822-8b55-0d79cd3045e1", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022795", "expiresAt": null, "viewCount": 476, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "2d77a95e-fd06-4aa8-aaea-31fa0e08211d", "companyId": "7bb1bcc2-6dc1-4ce1-add9-b7e767481da7", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022802", "expiresAt": null, "viewCount": 493, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "843bee13-d8ca-408b-bba2-f20832b50733", "companyId": "c0ac624a-f19b-494b-9957-e868db761f6f", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022808", "expiresAt": null, "viewCount": 510, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "c6a098da-6c52-40b3-89bc-b4272fd7f168", "companyId": "4f15f43d-1c94-4b89-be83-d23ded9acb1e", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022815", "expiresAt": null, "viewCount": 527, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "c83c4e84-489f-4d34-b9ef-e77330a92a07", "companyId": "3d88ac25-2ffa-4a22-b8c1-75dd0a89186d", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022821", "expiresAt": null, "viewCount": 544, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "f86b1d3f-d122-4842-9db5-b74f54beebb1", "companyId": "7b99264c-c034-40a4-b4d4-65204f4235a7", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022828", "expiresAt": null, "viewCount": 561, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "413de534-98bf-4a12-8d00-61c542fb8a4b", "companyId": "fa635ac1-758c-4df2-a506-e747f6c8d8ac", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022834", "expiresAt": null, "viewCount": 578, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "14a4d7ab-d9ea-4bcf-8bfb-be1e3dad3c76", "companyId": "5446b769-e122-4ae4-ba17-0b5904cdd61e", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022841", "expiresAt": null, "viewCount": 595, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "da0f452f-546a-4f33-a628-0e6f9228d06f", "companyId": "9785d6dc-05ca-48b4-a55a-84b6d28e43f0", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022847", "expiresAt": null, "viewCount": 612, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "e85f1595-fd93-4d93-bde0-bd36e35693bf", "companyId": "c31045a1-19de-479b-a4da-a32476617640", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022854", "expiresAt": null, "viewCount": 629, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "16bc90f0-b34f-42bb-973b-a07c44d18f4c", "companyId": "513042ef-0d07-4f7c-a655-1cf33d7ee126", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022861", "expiresAt": null, "viewCount": 646, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "9039041c-edd3-408b-babd-c208f42cf42a", "companyId": "da3d42a2-81c8-4a45-a362-8861d0ac1db3", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022867", "expiresAt": null, "viewCount": 663, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "5b6cee77-df79-46b4-9c47-3d4ba89f35d1", "companyId": "7f84aa21-026c-4d77-b72b-08fa60c10a08", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022874", "expiresAt": null, "viewCount": 680, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "82cd5295-3f34-47f1-bddf-74ab40371b2c", "companyId": "04329498-f953-42c7-8828-8cb225fc3456", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022881", "expiresAt": null, "viewCount": 697, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "62b3c8a4-ad96-4956-a7bd-ea5522fb0a08", "companyId": "2b59e33a-2ef0-45ed-80db-dda656d2a045", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022887", "expiresAt": null, "viewCount": 714, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "13d34545-a65d-40c7-9d97-1e51438a5fad", "companyId": "6d9250ca-9898-4b12-98a1-edb90d23e77a", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022894", "expiresAt": null, "viewCount": 731, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "85c9e7d7-c992-470e-8fc9-1277d658dc79", "companyId": "61d26503-1385-40db-bb2f-c2f3b3b6cfc9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022901", "expiresAt": null, "viewCount": 748, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "e1eebc13-aba1-4201-83a7-4ecb76bb75ff", "companyId": "f7bb60a9-aeb1-4398-a3cc-093a0791be1e", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022907", "expiresAt": null, "viewCount": 765, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "63058819-02c9-4125-8fb4-8747bf488000", "companyId": "53619a4c-ab32-4215-9729-074bf961a995", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022915", "expiresAt": null, "viewCount": 782, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "c659c9a6-7bc3-4c06-a5b7-39f4b9ae73e2", "companyId": "fb08a870-4cef-40d6-a846-103b4227db2b", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022922", "expiresAt": null, "viewCount": 799, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "96e5cfd3-6fa8-44e4-9241-63abb2ee9d72", "companyId": "0879b0c5-3b5e-4df6-a1b9-437243ae7695", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022929", "expiresAt": null, "viewCount": 816, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "ebfd4407-45e0-4f31-be5c-f36259b422f3", "companyId": "4e435fa3-d7dd-4cf3-b2c0-7bd460e78841", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022935", "expiresAt": null, "viewCount": 833, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "f5593e46-d118-462d-935d-291080a7f128", "companyId": "cd41d44d-588e-431b-a91c-c3d3d8dfc89d", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022942", "expiresAt": null, "viewCount": 850, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "e35cf802-825c-48a9-b925-00bf7f8d8806", "companyId": "316ae71b-f552-4c60-8880-685fdef5b3d7", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022949", "expiresAt": null, "viewCount": 867, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "569e4a0b-3171-4eac-ab39-ab393ed805c2", "companyId": "b5858630-ee9e-45c4-8b36-a2c5da554717", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022956", "expiresAt": null, "viewCount": 884, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "8489299b-3e66-48bd-bf24-336af3c5e634", "companyId": "f076fffc-e914-40cb-b535-c749ccd6482f", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022963", "expiresAt": null, "viewCount": 901, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "980248db-b802-4470-a7b6-ac88a4bfb09b", "companyId": "83ac64cb-3eb4-49ac-8176-d9641907bed9", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022970", "expiresAt": null, "viewCount": 918, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "948f68f0-bbc1-4122-a29d-d78ffc7c1ed3", "companyId": "63986696-ed5b-4660-a849-681935fda1a7", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022976", "expiresAt": null, "viewCount": 935, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "c387a7a9-85d8-46ca-8b64-17bcb7863193", "companyId": "f7796c5a-4078-4607-b794-8fe7f4899071", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022982", "expiresAt": null, "viewCount": 952, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "96b13b83-ee43-474a-bb40-3764741cf127", "companyId": "37ea4ec7-3539-41c2-bf3c-0ca373249b0e", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022989", "expiresAt": null, "viewCount": 969, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "ad283cbc-6f07-4341-b43d-e9a072d8dc12", "companyId": "36bb8c0a-3af2-4ac0-b9ec-9a8a2fbeede1", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.022996", "expiresAt": null, "viewCount": 986, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "dafaf88b-c56a-4161-bfac-1e13fd01369a", "companyId": "993ed64a-641f-4132-b5ad-40e69c45a311", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023002", "expiresAt": null, "viewCount": 3, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "197447a0-3dc9-43d7-9e92-dbc8e908a252", "companyId": "b943a2a6-e3b5-4f3f-9fff-1b34cb25f76d", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023009", "expiresAt": null, "viewCount": 20, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "550a1f2b-a489-4f44-9591-9811f0b932f4", "companyId": "f03090b8-7977-42b3-9072-7f6b2803c5cb", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023015", "expiresAt": null, "viewCount": 37, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "eab8e024-a129-47f5-9be7-36acb9a82e06", "companyId": "60dcf246-6467-4b53-88b7-0b1c6e1ebaea", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023022", "expiresAt": null, "viewCount": 54, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "ac9acc47-150b-42a4-b69d-5109230c6a0f", "companyId": "5c3dbb0a-3b73-43b2-9e3c-50dddaa506c8", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023028", "expiresAt": null, "viewCount": 71, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "4a8559af-9391-434a-b0f5-d6314f382eec", "companyId": "d92e0b47-f2ab-47f3-acab-ba90e7d3448f", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023035", "expiresAt": null, "viewCount": 88, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "4b9a28b3-3b53-44e2-bcf1-697562d8e7bb", "companyId": "f3100a86-8fb5-4880-93b8-22028c42a832", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023042", "expiresAt": null, "viewCount": 105, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "ab29d0e7-23bb-42d9-80ac-e3c641a3450d", "companyId": "09389b19-408f-4c77-90cd-9299d13577a1", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023048", "expiresAt": null, "viewCount": 122, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "7282d2c1-b95e-4c8a-9ccf-ba4b4befeb3a", "companyId": "960fb80a-b02a-47f2-95e6-336f3e262e4a", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023055", "expiresAt": null, "viewCount": 139, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "86fd11f5-56dd-44ef-8a7f-1868beab934a", "companyId": "7a5a370b-b707-499c-9613-c7b422a07b0c", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023061", "expiresAt": null, "viewCount": 156, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "7eba6056-68df-46ab-9d18-4d1fb944c280", "companyId": "7c91ff89-15fa-4f2d-b325-672e4ffa83a9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023067", "expiresAt": null, "viewCount": 173, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "a0a32026-0f46-4009-bcd2-8b6d52691958", "companyId": "b88f03d1-6463-4987-8972-0db2c64d40af", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023074", "expiresAt": null, "viewCount": 190, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "debd2575-733f-45dd-91d3-1f3beb54591e", "companyId": "89924d54-c2b0-45a7-858f-d8c748b9bbab", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023081", "expiresAt": null, "viewCount": 207, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "1250308e-c0c0-46f0-b2d0-ba38c408d48d", "companyId": "65e93fbc-4d7d-4dc7-aa33-4cbbcb74f55a", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023087", "expiresAt": null, "viewCount": 224, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "c8dfc7ad-1dac-4a52-b495-1a218691d9f7", "companyId": "ec37c511-ca75-4f24-8edc-71c86d41e664", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023094", "expiresAt": null, "viewCount": 241, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "7b79eeca-e036-4195-a4ae-3239dafba167", "companyId": "3cad333b-560a-477c-b621-1f9024b0d29f", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023100", "expiresAt": null, "viewCount": 258, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "1d638853-d91a-46f1-b0ec-1ca73892c869", "companyId": "4d6d0617-ecfa-4b54-9e94-dad160f59f11", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023107", "expiresAt": null, "viewCount": 275, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "bf60b74c-d8d4-4793-b1e7-5974063c2424", "companyId": "58faf246-c7ed-485b-8e0c-353fa9671d12", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023113", "expiresAt": null, "viewCount": 292, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "52aae816-eb1f-4c40-8120-0a96142bcb16", "companyId": "8845db93-f7df-49fe-94f4-021955487d74", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023120", "expiresAt": null, "viewCount": 309, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "c1d5219e-192c-4caa-b1ff-7f1ac8cc5b1e", "companyId": "6710cf7f-a793-4822-8b55-0d79cd3045e1", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023127", "expiresAt": null, "viewCount": 326, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "74514371-5418-4e31-9200-f97178ed143c", "companyId": "7bb1bcc2-6dc1-4ce1-add9-b7e767481da7", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023133", "expiresAt": null, "viewCount": 343, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "0492fe38-b5f1-47fd-a328-1875c0cd0671", "companyId": "c0ac624a-f19b-494b-9957-e868db761f6f", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023141", "expiresAt": null, "viewCount": 360, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "270929cb-259e-4718-becf-0a99e00e572d", "companyId": "4f15f43d-1c94-4b89-be83-d23ded9acb1e", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023148", "expiresAt": null, "viewCount": 377, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "1bbfeae3-3862-4d45-bb9a-02f83da6dad9", "companyId": "3d88ac25-2ffa-4a22-b8c1-75dd0a89186d", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023155", "expiresAt": null, "viewCount": 394, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "454bcd41-f09d-476d-89fe-ac00e2699ae8", "companyId": "7b99264c-c034-40a4-b4d4-65204f4235a7", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023161", "expiresAt": null, "viewCount": 411, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "e17d56de-e124-4c6a-b138-663cf345bf39", "companyId": "fa635ac1-758c-4df2-a506-e747f6c8d8ac", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023168", "expiresAt": null, "viewCount": 428, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "73766638-780e-4c0e-902b-ad1c7c70f0ce", "companyId": "5446b769-e122-4ae4-ba17-0b5904cdd61e", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023174", "expiresAt": null, "viewCount": 445, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "9a3def18-d07b-47dd-a577-ba8ae9299ed8", "companyId": "9785d6dc-05ca-48b4-a55a-84b6d28e43f0", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023181", "expiresAt": null, "viewCount": 462, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "9bb0f5ae-0253-4f98-a645-cd173d5ff204", "companyId": "c31045a1-19de-479b-a4da-a32476617640", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023187", "expiresAt": null, "viewCount": 479, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "0666871a-c4b7-4a30-a9ed-7dcb7f66ea85", "companyId": "513042ef-0d07-4f7c-a655-1cf33d7ee126", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023193", "expiresAt": null, "viewCount": 496, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "3dd5fda5-0c2c-41d1-a5bf-ca80f04587d0", "companyId": "da3d42a2-81c8-4a45-a362-8861d0ac1db3", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023201", "expiresAt": null, "viewCount": 513, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "3ea56294-8fe5-4a5d-9f4f-83efa72e5ea6", "companyId": "7f84aa21-026c-4d77-b72b-08fa60c10a08", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023208", "expiresAt": null, "viewCount": 530, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "3867ccaf-f7cc-4764-b703-0eae81565b71", "companyId": "04329498-f953-42c7-8828-8cb225fc3456", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023214", "expiresAt": null, "viewCount": 547, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "7cd94c29-b178-44de-accb-9638aa645ca9", "companyId": "2b59e33a-2ef0-45ed-80db-dda656d2a045", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023221", "expiresAt": null, "viewCount": 564, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "07124f46-ebf0-4894-a7a1-6e6666095ba5", "companyId": "6d9250ca-9898-4b12-98a1-edb90d23e77a", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023228", "expiresAt": null, "viewCount": 581, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "4e617979-c4bc-47c3-a9e1-a37ed1f6d45e", "companyId": "61d26503-1385-40db-bb2f-c2f3b3b6cfc9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023234", "expiresAt": null, "viewCount": 598, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "bdf67b15-61ec-4cfd-a62d-7d0e77ca1f9b", "companyId": "f7bb60a9-aeb1-4398-a3cc-093a0791be1e", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023241", "expiresAt": null, "viewCount": 615, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "787779e6-fbd1-4c2c-8ffd-dd5454d6563e", "companyId": "53619a4c-ab32-4215-9729-074bf961a995", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023247", "expiresAt": null, "viewCount": 632, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "c6a57b4f-0f7e-482d-9f8a-a03dc6901573", "companyId": "fb08a870-4cef-40d6-a846-103b4227db2b", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023254", "expiresAt": null, "viewCount": 649, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "a195cb9a-127e-4291-be48-4c55286e7997", "companyId": "0879b0c5-3b5e-4df6-a1b9-437243ae7695", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023260", "expiresAt": null, "viewCount": 666, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "3afb9645-a758-449b-91e4-0d79d999682a", "companyId": "4e435fa3-d7dd-4cf3-b2c0-7bd460e78841", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023267", "expiresAt": null, "viewCount": 683, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "d2d05a7b-6b75-478c-9050-981d5e25343c", "companyId": "cd41d44d-588e-431b-a91c-c3d3d8dfc89d", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023273", "expiresAt": null, "viewCount": 700, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "8247e010-1a14-49c6-a57f-c2ea5d8ed654", "companyId": "316ae71b-f552-4c60-8880-685fdef5b3d7", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023280", "expiresAt": null, "viewCount": 717, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "45c1ff78-b687-4603-8d54-9baffd04527a", "companyId": "b5858630-ee9e-45c4-8b36-a2c5da554717", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023286", "expiresAt": null, "viewCount": 734, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "3ee15c81-0527-4176-abf3-f04ac6466999", "companyId": "f076fffc-e914-40cb-b535-c749ccd6482f", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023293", "expiresAt": null, "viewCount": 751, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "8b51ee69-c7ba-4f1c-8119-84a0506d5bdd", "companyId": "83ac64cb-3eb4-49ac-8176-d9641907bed9", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023299", "expiresAt": null, "viewCount": 768, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "8cb901aa-8d9a-4c96-8a04-3f61686f299a", "companyId": "63986696-ed5b-4660-a849-681935fda1a7", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023306", "expiresAt": null, "viewCount": 785, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "f2ed6f08-f683-49c7-b156-27be6e6a6be9", "companyId": "f7796c5a-4078-4607-b794-8fe7f4899071", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023312", "expiresAt": null, "viewCount": 802, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "e1091ad5-29df-43f6-8b38-3a2a8774d2c8", "companyId": "37ea4ec7-3539-41c2-bf3c-0ca373249b0e", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023396", "expiresAt": null, "viewCount": 819, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "e0745590-09bb-4bfd-ab1e-e7f0a2f26671", "companyId": "36bb8c0a-3af2-4ac0-b9ec-9a8a2fbeede1", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023405", "expiresAt": null, "viewCount": 836, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "c572dd21-8528-4d28-907d-807de068f32e", "companyId": "993ed64a-641f-4132-b5ad-40e69c45a311", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023412", "expiresAt": null, "viewCount": 853, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "5c67843a-0e80-4c64-8323-82d8eeb3af48", "companyId": "b943a2a6-e3b5-4f3f-9fff-1b34cb25f76d", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023419", "expiresAt": null, "viewCount": 870, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "12c4b5a1-8cb7-405c-80ec-6cd4ad40bca3", "companyId": "f03090b8-7977-42b3-9072-7f6b2803c5cb", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023425", "expiresAt": null, "viewCount": 887, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "aeae2b44-c529-4eca-8106-a52bcb4b1df2", "companyId": "60dcf246-6467-4b53-88b7-0b1c6e1ebaea", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023432", "expiresAt": null, "viewCount": 904, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "bec7eccd-7361-44ae-9124-389d6c747186", "companyId": "5c3dbb0a-3b73-43b2-9e3c-50dddaa506c8", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023438", "expiresAt": null, "viewCount": 921, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "9529b761-85c9-4d66-8a3f-c7750cddf40e", "companyId": "d92e0b47-f2ab-47f3-acab-ba90e7d3448f", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023445", "expiresAt": null, "viewCount": 938, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "ad49693b-59a0-4c2e-b4fb-ccefdeafeb28", "companyId": "f3100a86-8fb5-4880-93b8-22028c42a832", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023451", "expiresAt": null, "viewCount": 955, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "eeb71f1f-e4c4-4840-8888-0df23a40e397", "companyId": "09389b19-408f-4c77-90cd-9299d13577a1", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023458", "expiresAt": null, "viewCount": 972, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "fc494f40-6cdf-4a7b-a468-fd1b9a88af31", "companyId": "960fb80a-b02a-47f2-95e6-336f3e262e4a", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023464", "expiresAt": null, "viewCount": 989, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "9819bd5e-1fb3-4ccc-93e7-4d50ae4dcd84", "companyId": "7a5a370b-b707-499c-9613-c7b422a07b0c", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023470", "expiresAt": null, "viewCount": 6, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "f2e716f0-8f5f-4d07-9195-017aecba8e67", "companyId": "7c91ff89-15fa-4f2d-b325-672e4ffa83a9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023477", "expiresAt": null, "viewCount": 23, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "12eb5734-9f6e-404e-8f66-55c5a4acf7bb", "companyId": "b88f03d1-6463-4987-8972-0db2c64d40af", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023483", "expiresAt": null, "viewCount": 40, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "c4b43960-988d-47d5-98f7-3cedaf4d0233", "companyId": "89924d54-c2b0-45a7-858f-d8c748b9bbab", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023490", "expiresAt": null, "viewCount": 57, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "ffecdc21-33b3-4423-9e42-4ecb444bc21e", "companyId": "65e93fbc-4d7d-4dc7-aa33-4cbbcb74f55a", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023496", "expiresAt": null, "viewCount": 74, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "7c83efc7-897c-4bbc-96d4-15996713a214", "companyId": "ec37c511-ca75-4f24-8edc-71c86d41e664", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023503", "expiresAt": null, "viewCount": 91, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "d91e05dd-9907-4e2a-8ec0-b83f0699c567", "companyId": "3cad333b-560a-477c-b621-1f9024b0d29f", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023509", "expiresAt": null, "viewCount": 108, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "54e4f023-346e-47fe-9690-39016bccf78b", "companyId": "4d6d0617-ecfa-4b54-9e94-dad160f59f11", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023515", "expiresAt": null, "viewCount": 125, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "d79ef8a0-a084-4b0a-81c4-714d425a162a", "companyId": "58faf246-c7ed-485b-8e0c-353fa9671d12", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023522", "expiresAt": null, "viewCount": 142, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "1670068f-2317-4d4a-bb82-ca7abfd9e3ad", "companyId": "8845db93-f7df-49fe-94f4-021955487d74", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023530", "expiresAt": null, "viewCount": 159, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "54de0466-79c2-4657-a55c-64a766847c21", "companyId": "6710cf7f-a793-4822-8b55-0d79cd3045e1", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023536", "expiresAt": null, "viewCount": 176, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "a73605d0-3a6c-408e-8ee7-765b256363fd", "companyId": "7bb1bcc2-6dc1-4ce1-add9-b7e767481da7", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023543", "expiresAt": null, "viewCount": 193, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "92a383a7-233b-4720-a615-d938c1d0847d", "companyId": "c0ac624a-f19b-494b-9957-e868db761f6f", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023549", "expiresAt": null, "viewCount": 210, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "07b1e695-79fd-4998-9390-1f3548655320", "companyId": "4f15f43d-1c94-4b89-be83-d23ded9acb1e", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023556", "expiresAt": null, "viewCount": 227, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "ad5019cb-5bd7-41c3-b39b-43c309641161", "companyId": "3d88ac25-2ffa-4a22-b8c1-75dd0a89186d", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023563", "expiresAt": null, "viewCount": 244, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "b099317a-3892-4fe4-b8db-669c42d34437", "companyId": "7b99264c-c034-40a4-b4d4-65204f4235a7", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023569", "expiresAt": null, "viewCount": 261, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "140a1667-3f56-44a8-b980-ed60c4c45d24", "companyId": "fa635ac1-758c-4df2-a506-e747f6c8d8ac", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023576", "expiresAt": null, "viewCount": 278, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "3c6c7f03-118b-470e-b489-f86de617edb9", "companyId": "5446b769-e122-4ae4-ba17-0b5904cdd61e", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023582", "expiresAt": null, "viewCount": 295, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "e42c05d2-0023-4897-bdfa-fc537448a3f9", "companyId": "9785d6dc-05ca-48b4-a55a-84b6d28e43f0", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023589", "expiresAt": null, "viewCount": 312, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "0a74dd5f-33ee-4c75-a34f-061cfc6a6ef7", "companyId": "c31045a1-19de-479b-a4da-a32476617640", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023597", "expiresAt": null, "viewCount": 329, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "907c2788-5889-4987-9af9-e2e745c52a52", "companyId": "513042ef-0d07-4f7c-a655-1cf33d7ee126", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023603", "expiresAt": null, "viewCount": 346, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "036e28db-89dc-4521-a8ea-876108ce0e71", "companyId": "da3d42a2-81c8-4a45-a362-8861d0ac1db3", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023610", "expiresAt": null, "viewCount": 363, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "11be12ac-2848-464a-8baa-7a5521701b5d", "companyId": "7f84aa21-026c-4d77-b72b-08fa60c10a08", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023616", "expiresAt": null, "viewCount": 380, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "45e7bd12-9649-42ea-8216-f902147054f6", "companyId": "04329498-f953-42c7-8828-8cb225fc3456", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023623", "expiresAt": null, "viewCount": 397, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "964faf01-c389-474d-a487-69dc137ab19a", "companyId": "2b59e33a-2ef0-45ed-80db-dda656d2a045", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023629", "expiresAt": null, "viewCount": 414, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "fc2ae8a3-d56d-4472-91db-c106532cd543", "companyId": "6d9250ca-9898-4b12-98a1-edb90d23e77a", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023635", "expiresAt": null, "viewCount": 431, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "19432a20-521b-4732-812f-8ad7e417181e", "companyId": "61d26503-1385-40db-bb2f-c2f3b3b6cfc9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023642", "expiresAt": null, "viewCount": 448, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "bca2fca8-006a-4d2d-98cf-536a4572e1af", "companyId": "f7bb60a9-aeb1-4398-a3cc-093a0791be1e", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023648", "expiresAt": null, "viewCount": 465, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "8c9461f3-c7e8-4ec3-93a2-222565c7afb4", "companyId": "53619a4c-ab32-4215-9729-074bf961a995", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023655", "expiresAt": null, "viewCount": 482, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "0ec93e34-6c57-40ee-8664-377328a72738", "companyId": "fb08a870-4cef-40d6-a846-103b4227db2b", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023661", "expiresAt": null, "viewCount": 499, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "77cc07ee-8d3a-4892-abd4-59e8ea496a66", "companyId": "0879b0c5-3b5e-4df6-a1b9-437243ae7695", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023668", "expiresAt": null, "viewCount": 516, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "4da28737-4904-408b-b797-60d6e9b81d4e", "companyId": "4e435fa3-d7dd-4cf3-b2c0-7bd460e78841", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023674", "expiresAt": null, "viewCount": 533, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "55d5fa31-bc6d-41e0-82e6-a8a8f192bd00", "companyId": "cd41d44d-588e-431b-a91c-c3d3d8dfc89d", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023681", "expiresAt": null, "viewCount": 550, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "c708cc86-397a-4be1-8ee6-7d8547cd4822", "companyId": "316ae71b-f552-4c60-8880-685fdef5b3d7", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023687", "expiresAt": null, "viewCount": 567, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "e3623474-7238-450f-bb80-c82a1e768c9b", "companyId": "b5858630-ee9e-45c4-8b36-a2c5da554717", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023695", "expiresAt": null, "viewCount": 584, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "44f3bf3c-4a01-401f-ace7-c6ee06ce4e9f", "companyId": "f076fffc-e914-40cb-b535-c749ccd6482f", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023702", "expiresAt": null, "viewCount": 601, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "9a61642c-dfbe-457f-ad64-785fa73c0ee1", "companyId": "83ac64cb-3eb4-49ac-8176-d9641907bed9", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023708", "expiresAt": null, "viewCount": 618, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "0ad00986-a141-49a8-a6f8-1e5f6114bb2d", "companyId": "63986696-ed5b-4660-a849-681935fda1a7", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023715", "expiresAt": null, "viewCount": 635, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "4224eb75-c39d-42ca-8b79-a189682fe427", "companyId": "f7796c5a-4078-4607-b794-8fe7f4899071", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023721", "expiresAt": null, "viewCount": 652, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "ae817aea-3cc8-478c-b877-8e85b04b2c39", "companyId": "37ea4ec7-3539-41c2-bf3c-0ca373249b0e", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023728", "expiresAt": null, "viewCount": 669, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "1fa16ae1-9c29-46ed-8b94-44d06b2e1c3c", "companyId": "36bb8c0a-3af2-4ac0-b9ec-9a8a2fbeede1", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023734", "expiresAt": null, "viewCount": 686, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "412bcc5e-ee88-412a-9c00-4931538fe926", "companyId": "993ed64a-641f-4132-b5ad-40e69c45a311", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023741", "expiresAt": null, "viewCount": 703, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "40280fa7-c9a2-468b-a7d4-754365e8fe9d", "companyId": "b943a2a6-e3b5-4f3f-9fff-1b34cb25f76d", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023747", "expiresAt": null, "viewCount": 720, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "2d0428bd-ac7e-4fd8-a88f-031de931fe03", "companyId": "f03090b8-7977-42b3-9072-7f6b2803c5cb", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023754", "expiresAt": null, "viewCount": 737, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "59206498-cf84-4dba-b516-c026ad37f971", "companyId": "60dcf246-6467-4b53-88b7-0b1c6e1ebaea", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023760", "expiresAt": null, "viewCount": 754, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "af4b394f-a03c-46ef-ba6a-7b5cede2168b", "companyId": "5c3dbb0a-3b73-43b2-9e3c-50dddaa506c8", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023767", "expiresAt": null, "viewCount": 771, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "58037655-daed-4b54-a7c9-177188471340", "companyId": "d92e0b47-f2ab-47f3-acab-ba90e7d3448f", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023773", "expiresAt": null, "viewCount": 788, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "8cae03b8-efdf-43df-bf9f-8e25732c62e8", "companyId": "f3100a86-8fb5-4880-93b8-22028c42a832", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023781", "expiresAt": null, "viewCount": 805, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "e2a20232-5c74-4020-b067-cfe7008d149e", "companyId": "09389b19-408f-4c77-90cd-9299d13577a1", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023787", "expiresAt": null, "viewCount": 822, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "52b6b3bd-c50e-47ae-b414-be3964867319", "companyId": "960fb80a-b02a-47f2-95e6-336f3e262e4a", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023794", "expiresAt": null, "viewCount": 839, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "c76879db-00ab-40c5-a2b2-f1a03fb15e97", "companyId": "7a5a370b-b707-499c-9613-c7b422a07b0c", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023800", "expiresAt": null, "viewCount": 856, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "ca7a3480-4100-4c9c-a732-ef3d1fcdb547", "companyId": "7c91ff89-15fa-4f2d-b325-672e4ffa83a9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023807", "expiresAt": null, "viewCount": 873, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "3aea1780-2564-4b51-b07f-64119f750603", "companyId": "b88f03d1-6463-4987-8972-0db2c64d40af", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023813", "expiresAt": null, "viewCount": 890, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "dfebdb4e-3f30-462e-a913-7210f03519e6", "companyId": "89924d54-c2b0-45a7-858f-d8c748b9bbab", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023820", "expiresAt": null, "viewCount": 907, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "351678cc-4ce2-4866-8fba-fc115ba2ee3b", "companyId": "65e93fbc-4d7d-4dc7-aa33-4cbbcb74f55a", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023826", "expiresAt": null, "viewCount": 924, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "27ceba4f-3eb5-4b9f-9646-075727176ba2", "companyId": "ec37c511-ca75-4f24-8edc-71c86d41e664", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023833", "expiresAt": null, "viewCount": 941, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "aca29c39-b80e-4fff-a2f3-96e8989f5125", "companyId": "3cad333b-560a-477c-b621-1f9024b0d29f", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023839", "expiresAt": null, "viewCount": 958, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "7c1f9f59-5f5c-409a-9030-19aa79dc4de7", "companyId": "4d6d0617-ecfa-4b54-9e94-dad160f59f11", "title": "Backend Developer (Java, Spring Boot)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023846", "expiresAt": null, "viewCount": 975, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "1 năm"}, {"id": "24ac6ea3-3f48-471c-a2d1-46a48780d5dd", "companyId": "58faf246-c7ed-485b-8e0c-353fa9671d12", "title": "Frontend Developer (ReactJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023852", "expiresAt": null, "viewCount": 992, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "dcbf24d7-2cac-44ac-b477-46a404a68490", "companyId": "8845db93-f7df-49fe-94f4-021955487d74", "title": "Fullstack Developer (NextJS)", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023859", "expiresAt": null, "viewCount": 9, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "bd151188-1325-47cd-bcb2-db899ec22bf6", "companyId": "6710cf7f-a793-4822-8b55-0d79cd3045e1", "title": "AI Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023865", "expiresAt": null, "viewCount": 26, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "7ffe7768-9a7f-4dd9-92fb-4bba3cec30b4", "companyId": "7bb1bcc2-6dc1-4ce1-add9-b7e767481da7", "title": "Senior Java Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023871", "expiresAt": null, "viewCount": 43, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["Java", "Spring Boot", "MySQL", "Git"], "experience": "5 năm"}, {"id": "fcd197b8-9f6d-4fc1-818d-78c53dd17b5c", "companyId": "c0ac624a-f19b-494b-9957-e868db761f6f", "title": "Flutter Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023878", "expiresAt": null, "viewCount": 60, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "2f8fcf48-31f7-4a45-a111-dfad8437d394", "companyId": "4f15f43d-1c94-4b89-be83-d23ded9acb1e", "title": "Business Analyst", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023884", "expiresAt": null, "viewCount": 77, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "e90fc597-c76a-4b45-88f7-50f6a109dd00", "companyId": "3d88ac25-2ffa-4a22-b8c1-75dd0a89186d", "title": "Android Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023890", "expiresAt": null, "viewCount": 94, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "17276e83-72e9-46e6-bd7d-259230a347f1", "companyId": "7b99264c-c034-40a4-b4d4-65204f4235a7", "title": "iOS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023897", "expiresAt": null, "viewCount": 111, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "97755612-b8f7-4a3c-9ab5-6bc909193370", "companyId": "fa635ac1-758c-4df2-a506-e747f6c8d8ac", "title": "DevOps Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023903", "expiresAt": null, "viewCount": 128, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "6949d97f-a2bd-449e-a3fa-b294f319d829", "companyId": "5446b769-e122-4ae4-ba17-0b5904cdd61e", "title": "Data Scientist", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023910", "expiresAt": null, "viewCount": 145, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "39a27aa8-6312-425f-9837-b18e6934ab5f", "companyId": "9785d6dc-05ca-48b4-a55a-84b6d28e43f0", "title": "Machine Learning Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023916", "expiresAt": null, "viewCount": 162, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "5fe0b295-5948-4f44-993a-03ea61c98b10", "companyId": "c31045a1-19de-479b-a4da-a32476617640", "title": "Python Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023922", "expiresAt": null, "viewCount": 179, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "da9e50f7-654e-4b40-b6ea-ac32ced85dbc", "companyId": "513042ef-0d07-4f7c-a655-1cf33d7ee126", "title": "NodeJS Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023929", "expiresAt": null, "viewCount": 196, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "da0f05ab-c02f-450c-afb7-01b5e5761bf3", "companyId": "da3d42a2-81c8-4a45-a362-8861d0ac1db3", "title": "PHP Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023935", "expiresAt": null, "viewCount": 213, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "b40790e4-dcab-4b49-9e08-37b7d5580312", "companyId": "7f84aa21-026c-4d77-b72b-08fa60c10a08", "title": "React Native Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023942", "expiresAt": null, "viewCount": 230, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "b3db7573-1f1e-442a-918f-c82e1ca87bbc", "companyId": "04329498-f953-42c7-8828-8cb225fc3456", "title": "Unity Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023948", "expiresAt": null, "viewCount": 247, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "98bbe8b2-df25-4685-82e9-76f64dcb4a9d", "companyId": "2b59e33a-2ef0-45ed-80db-dda656d2a045", "title": "Game Developer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023954", "expiresAt": null, "viewCount": 264, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "47f77e59-c759-4d21-a04b-b2b4373a1915", "companyId": "6d9250ca-9898-4b12-98a1-edb90d23e77a", "title": "QA Engineer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023961", "expiresAt": null, "viewCount": 281, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "415c9576-0bd0-416e-818b-07fc60ddb234", "companyId": "61d26503-1385-40db-bb2f-c2f3b3b6cfc9", "title": "Software Architect", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 22500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023970", "expiresAt": null, "viewCount": 298, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}, {"id": "fb3262b0-98fc-47ce-972d-abb089737095", "companyId": "f7bb60a9-aeb1-4398-a3cc-093a0791be1e", "title": "Technical Leader", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 27500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023976", "expiresAt": null, "viewCount": 315, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "1 năm"}, {"id": "519f99c2-7f31-48af-baff-5efecfd5b775", "companyId": "53619a4c-ab32-4215-9729-074bf961a995", "title": "Project Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 40000000, "location": "<PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023983", "expiresAt": null, "viewCount": 332, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "2 năm"}, {"id": "9add0911-9556-4379-ab86-c717e54a7a76", "companyId": "fb08a870-4cef-40d6-a846-103b4227db2b", "title": "Product Manager", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": null, "location": "<PERSON><PERSON> <PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023990", "expiresAt": null, "viewCount": 349, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "3 năm"}, {"id": "7dcbe35c-9b50-4439-ac89-53382b6291a9", "companyId": "0879b0c5-3b5e-4df6-a1b9-437243ae7695", "title": "UI/UX Designer", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 12500000, "location": "Đà Nẵng", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.023996", "expiresAt": null, "viewCount": 366, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "4 năm"}, {"id": "bbd3de31-90b1-43ec-adf5-6660e77cccbf", "companyId": "4e435fa3-d7dd-4cf3-b2c0-7bd460e78841", "title": "Database Administrator", "description": "<PERSON>ham gia phát triển các dự án công nghệ hiện đại. Làm việc với team chuyên nghiệp và năng động. <PERSON><PERSON> hội học hỏi và phát triển kỹ năng.", "salary": 17500000, "location": "<PERSON><PERSON><PERSON>", "jobType": "<PERSON><PERSON><PERSON> thời gian", "status": "Active", "postedAt": "2025-07-16T01:19:58.024002", "expiresAt": null, "viewCount": 383, "requirements": "Tốt nghiệp Đ<PERSON><PERSON> học chuyên ngành liên quan. <PERSON><PERSON> kinh nghiệm làm việc thực tế. <PERSON><PERSON> năng giao tiếp tốt.", "benefits": "Lương thưởng hấp dẫn. <PERSON><PERSON><PERSON> hiểm đầy đủ. <PERSON><PERSON><PERSON> trường làm việc năng động.", "skills": ["ReactJS", "JavaScript", "HTML", "CSS"], "experience": "5 năm"}], "metadata": {"crawled_at": "2025-07-16T01:19:58.024010", "total_companies": 50, "total_jobs": 200}}